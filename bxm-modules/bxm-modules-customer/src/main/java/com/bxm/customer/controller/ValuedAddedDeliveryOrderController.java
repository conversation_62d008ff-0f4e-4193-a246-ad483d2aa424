package com.bxm.customer.controller;

import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 增值交付单Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@RestController
@RequestMapping("/valuedAddedDeliveryOrder")
@Api(tags = "增值交付单管理")
public class ValuedAddedDeliveryOrderController extends BaseController {

    /**
     * 生成交付单编号
     *
     * 编号规则：VAD + yyMMddHHmm + 2位随机码
     * 总长度：3 + 10 + 2 = 15位
     *
     * 示例：VAD250802143012
     *
     * @return 生成的交付单编号
     */
    @GetMapping("/genDeliveryOrderNo")
    @ApiOperation(value = "生成交付单编号", notes = "生成唯一的增值交付单编号，格式：VAD+时间戳+随机码，总长度16位")
    @Log(title = "Generate delivery order number", businessType = BusinessType.OTHER)
    public AjaxResult genDeliveryOrderNo() {
        try {
            // 生成时间戳部分：yyMMddHHmm格式
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmm"));

            // 生成2位随机码
            String randomCode = StringUtils.generateRandomCode(3);

            // 组合生成最终编号
            String deliveryOrderNo = "VAD" + timestamp + randomCode;

            log.info("Generated delivery order number: {}", deliveryOrderNo);

            return success(deliveryOrderNo);

        } catch (Exception e) {
            log.error("Failed to generate delivery order number", e);
            return error("生成交付单编号失败");
        }
    }
}
