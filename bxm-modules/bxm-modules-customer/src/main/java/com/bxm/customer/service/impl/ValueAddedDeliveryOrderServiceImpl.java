package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.enums.ValueAddedItemType;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import com.bxm.customer.mapper.ValueAddedDeliveryOrderMapper;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 增值交付单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class ValueAddedDeliveryOrderServiceImpl extends ServiceImpl<ValueAddedDeliveryOrderMapper, ValueAddedDeliveryOrder> implements IValueAddedDeliveryOrderService
{

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueAddedDeliveryOrder upsert(ValueAddedDeliveryOrderVO orderVO) {
        try {
            log.info("Starting upsert operation for delivery order: {}", orderVO.getCustomerName());

            // 1. 基础验证
            validateOrderVO(orderVO);

            // 2. VO转换为DO
            ValueAddedDeliveryOrder order = new ValueAddedDeliveryOrder();
            BeanUtils.copyProperties(orderVO, order);

            // 3. 查找现有记录
            ValueAddedDeliveryOrder existingOrder = findExistingOrder(order);

            if (existingOrder != null) {
                // 更新现有记录
                log.info("Updating existing delivery order: {}", existingOrder.getDeliveryOrderNo());
                updateExistingOrder(existingOrder, order);
                updateById(existingOrder);
                return existingOrder;
            } else {
                // 创建新记录
                log.info("Creating new delivery order for customer: {}", order.getCustomerName());
                prepareNewOrder(order);
                save(order);
                log.info("Created new delivery order: {}", order.getDeliveryOrderNo());
                return order;
            }

        } catch (Exception e) {
            log.error("Failed to upsert delivery order for customer: {}", orderVO.getCustomerName(), e);
            throw new RuntimeException("保存增值交付单失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ValueAddedDeliveryOrder getByDeliveryOrderNo(String deliveryOrderNo) {
        if (StringUtils.isEmpty(deliveryOrderNo)) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedDeliveryOrder::getDeliveryOrderNo, deliveryOrderNo.trim())
                .eq(ValueAddedDeliveryOrder::getIsDel, false);

        return getOne(queryWrapper);
    }

    @Override
    public ValueAddedDeliveryOrder getByCustomerIdAndItemType(Long customerId, Integer valueAddedItemType) {
        if (customerId == null || valueAddedItemType == null) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedDeliveryOrder::getCustomerId, customerId)
                .eq(ValueAddedDeliveryOrder::getValueAddedItemType, valueAddedItemType)
                .eq(ValueAddedDeliveryOrder::getIsDel, false);

        return getOne(queryWrapper);
    }

    /**
     * 验证订单VO
     */
    private void validateOrderVO(ValueAddedDeliveryOrderVO orderVO) {
        // 验证增值事项类型
        if (!ValueAddedItemType.isValid(orderVO.getValueAddedItemType())) {
            throw new IllegalArgumentException("无效的增值事项类型: " + orderVO.getValueAddedItemType());
        }

        // 验证纳税性质
        if (orderVO.getTaxpayerType() != null &&
            (orderVO.getTaxpayerType() < 1 || orderVO.getTaxpayerType() > 2)) {
            throw new IllegalArgumentException("无效的纳税性质: " + orderVO.getTaxpayerType());
        }

        // 验证账期逻辑
        if (orderVO.getAccountingPeriodStart() != null && orderVO.getAccountingPeriodEnd() != null) {
            if (orderVO.getAccountingPeriodStart().isAfter(orderVO.getAccountingPeriodEnd())) {
                throw new IllegalArgumentException("账期开始时间不能晚于结束时间");
            }
        }
    }

    /**
     * 查找现有记录
     */
    private ValueAddedDeliveryOrder findExistingOrder(ValueAddedDeliveryOrder order) {
        // 1. 如果提供了ID，直接查询
        if (order.getId() != null) {
            return getById(order.getId());
        }

        // 2. 如果提供了交付单编号，按编号查询
        if (StringUtils.isNotEmpty(order.getDeliveryOrderNo())) {
            return getByDeliveryOrderNo(order.getDeliveryOrderNo());
        }

        // 3. 根据客户ID和增值事项查询（避免重复创建）
        if (order.getCustomerId() != null && order.getValueAddedItemType() != null) {
            return getByCustomerIdAndItemType(order.getCustomerId(), order.getValueAddedItemType());
        }

        return null;
    }

    /**
     * 更新现有记录
     */
    private void updateExistingOrder(ValueAddedDeliveryOrder existingOrder, ValueAddedDeliveryOrder newOrder) {
        // 保留原有的ID、交付单编号和创建相关信息
        Long originalId = existingOrder.getId();
        String originalDeliveryOrderNo = existingOrder.getDeliveryOrderNo();
        String originalCreateBy = existingOrder.getCreateBy();
        java.util.Date originalCreateTime = existingOrder.getCreateTime();

        // 使用BeanUtils复制所有属性
        BeanUtils.copyProperties(newOrder, existingOrder);

        // 恢复不应该被更新的字段
        existingOrder.setId(originalId);
        existingOrder.setDeliveryOrderNo(originalDeliveryOrderNo);
        existingOrder.setCreateBy(originalCreateBy);
        existingOrder.setCreateTime(originalCreateTime);
    }

    /**
     * 准备新记录
     */
    private void prepareNewOrder(ValueAddedDeliveryOrder order) {
        // 生成交付单编号（如果未提供）
        if (StringUtils.isEmpty(order.getDeliveryOrderNo())) {
            order.setDeliveryOrderNo(generateDeliveryOrderNo());
        }

        // 设置默认状态
        if (StringUtils.isEmpty(order.getStatus())) {
            order.setStatus("DRAFT"); // 草稿状态
        }

        // 设置默认删除标志
        order.setIsDel(false);
    }

    /**
     * 生成交付单编号
     */
    private String generateDeliveryOrderNo() {
        // 生成时间戳部分：yyMMddHHmm格式
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmm"));
        // 生成3位随机码
        String randomCode = StringUtils.generateRandomCode(3);
        // 组合生成最终编号
        return "VAD" + timestamp + randomCode;
    }
}
