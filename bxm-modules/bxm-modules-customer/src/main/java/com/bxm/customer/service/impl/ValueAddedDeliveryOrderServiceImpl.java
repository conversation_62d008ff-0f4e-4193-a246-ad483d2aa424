package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.mapper.ValueAddedDeliveryOrderMapper;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 增值交付单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class ValueAddedDeliveryOrderServiceImpl extends ServiceImpl<ValueAddedDeliveryOrderMapper, ValueAddedDeliveryOrder> implements IValueAddedDeliveryOrderService
{

}
