package com.bxm.customer.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 增值事项类型枚举
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Getter
@AllArgsConstructor
public enum ValueAddedItemType {

    /**
     * 社医保
     */
    SOCIAL_INSURANCE(1, "社医保"),

    /**
     * 个税明细
     */
    PERSONAL_TAX(2, "个税明细"),

    /**
     * 国税账号
     */
    NATIONAL_TAX_ACCOUNT(3, "国税账号"),

    /**
     * 个税账号
     */
    PERSONAL_TAX_ACCOUNT(4, "个税账号");

    private final Integer code;
    private final String name;

    /**
     * 根据代码获取枚举
     *
     * @param code 增值事项代码
     * @return 对应的枚举值
     */
    public static ValueAddedItemType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ValueAddedItemType itemType : values()) {
            if (itemType.getCode().equals(code)) {
                return itemType;
            }
        }
        return null;
    }

    /**
     * 验证增值事项代码是否有效
     *
     * @param code 增值事项代码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
