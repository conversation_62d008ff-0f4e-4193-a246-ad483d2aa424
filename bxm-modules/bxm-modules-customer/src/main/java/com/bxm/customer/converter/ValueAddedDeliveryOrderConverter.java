package com.bxm.customer.converter;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import org.springframework.stereotype.Component;

/**
 * 增值交付单转换器
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Component
public class ValueAddedDeliveryOrderConverter {

    /**
     * VO转换为DO
     *
     * @param vo VO对象
     * @return DO对象
     */
    public ValueAddedDeliveryOrder voToDo(ValueAddedDeliveryOrderVO vo) {
        if (vo == null) {
            return null;
        }

        ValueAddedDeliveryOrder order = new ValueAddedDeliveryOrder();
        order.setId(vo.getId());
        order.setDeliveryOrderNo(vo.getDeliveryOrderNo());
        order.setCustomerId(vo.getCustomerId());
        order.setCustomerName(vo.getCustomerName());
        order.setCreditCode(vo.getCreditCode());
        order.setTaxNo(vo.getTaxNo());
        order.setTaxpayerType(vo.getTaxpayerType());
        order.setValueAddedItemType(vo.getValueAddedItemType());
        order.setAccountingPeriodStart(vo.getAccountingPeriodStart());
        order.setAccountingPeriodEnd(vo.getAccountingPeriodEnd());
        order.setLocalTaxAccount(vo.getLocalTaxAccount());
        order.setReporterName(vo.getReporterName());
        order.setContactInfo(vo.getContactInfo());
        order.setSyncHandlingFee(vo.getSyncHandlingFee());
        order.setAccountTypes(vo.getAccountTypes());
        order.setRequirements(vo.getRequirements());
        order.setSyncReassignment(vo.getSyncReassignment());
        order.setModifyDueDate(vo.getModifyDueDate());
        order.setDdl(vo.getDdl());
        order.setOrgId(vo.getOrgId());
        order.setStatus(vo.getStatus());

        return order;
    }

    /**
     * DO转换为VO
     *
     * @param order DO对象
     * @return VO对象
     */
    public ValueAddedDeliveryOrderVO doToVo(ValueAddedDeliveryOrder order) {
        if (order == null) {
            return null;
        }

        return ValueAddedDeliveryOrderVO.builder()
                .id(order.getId())
                .deliveryOrderNo(order.getDeliveryOrderNo())
                .customerId(order.getCustomerId())
                .customerName(order.getCustomerName())
                .creditCode(order.getCreditCode())
                .taxNo(order.getTaxNo())
                .taxpayerType(order.getTaxpayerType())
                .valueAddedItemType(order.getValueAddedItemType())
                .accountingPeriodStart(order.getAccountingPeriodStart())
                .accountingPeriodEnd(order.getAccountingPeriodEnd())
                .localTaxAccount(order.getLocalTaxAccount())
                .reporterName(order.getReporterName())
                .contactInfo(order.getContactInfo())
                .syncHandlingFee(order.getSyncHandlingFee())
                .accountTypes(order.getAccountTypes())
                .requirements(order.getRequirements())
                .syncReassignment(order.getSyncReassignment())
                .modifyDueDate(order.getModifyDueDate())
                .ddl(order.getDdl())
                .orgId(order.getOrgId())
                .status(order.getStatus())
                .build();
    }
}
