package com.bxm.customer.converter;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * 增值交付单转换器
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Component
public class ValueAddedDeliveryOrderConverter {

    /**
     * VO转换为DO
     *
     * @param vo VO对象
     * @return DO对象
     */
    public ValueAddedDeliveryOrder voToDo(ValueAddedDeliveryOrderVO vo) {
        if (vo == null) {
            return null;
        }

        ValueAddedDeliveryOrder order = new ValueAddedDeliveryOrder();
        // 使用BeanUtils进行属性复制，自动匹配同名属性
        BeanUtils.copyProperties(vo, order);

        return order;
    }

    /**
     * DO转换为VO
     *
     * @param order DO对象
     * @return VO对象
     */
    public ValueAddedDeliveryOrderVO doToVo(ValueAddedDeliveryOrder order) {
        if (order == null) {
            return null;
        }

        return ValueAddedDeliveryOrderVO.builder()
                .id(order.getId())
                .deliveryOrderNo(order.getDeliveryOrderNo())
                .customerId(order.getCustomerId())
                .customerName(order.getCustomerName())
                .creditCode(order.getCreditCode())
                .taxNo(order.getTaxNo())
                .taxpayerType(order.getTaxpayerType())
                .valueAddedItemType(order.getValueAddedItemType())
                .accountingPeriodStart(order.getAccountingPeriodStart())
                .accountingPeriodEnd(order.getAccountingPeriodEnd())
                .localTaxAccount(order.getLocalTaxAccount())
                .reporterName(order.getReporterName())
                .contactInfo(order.getContactInfo())
                .syncHandlingFee(order.getSyncHandlingFee())
                .accountTypes(order.getAccountTypes())
                .requirements(order.getRequirements())
                .syncReassignment(order.getSyncReassignment())
                .modifyDueDate(order.getModifyDueDate())
                .ddl(order.getDdl())
                .orgId(order.getOrgId())
                .status(order.getStatus())
                .build();
    }
}
